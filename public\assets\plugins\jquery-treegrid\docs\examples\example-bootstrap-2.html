<!DOCTYPE html>
<html lang="en">
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <meta name="description" content="">
        <meta name="author" content="">
        <link rel="shortcut icon" href="../../assets/ico/favicon.png">

        <title>TreeGrid for Bootstrap 2.3.x</title>

        <!-- Bootstrap core CSS -->
        <link rel="stylesheet" href="../css/default.min.css">
        <link rel="stylesheet" href="../../css/jquery.treegrid.css">
        <link rel="stylesheet" href="bootstrap-2.3.2/css/bootstrap.css">

        <script type="text/javascript"  src="../js/jquery.min.js"></script>
        <script type="text/javascript" src="../../js/jquery.treegrid.js"></script>
        <script type="text/javascript" src="../../js/jquery.treegrid.bootstrap2.js"></script>
        
        <script type="text/javascript"  src="../js/highlight.min.js"></script>
		<script>hljs.initHighlightingOnLoad();</script>
        
        <script type="text/javascript">
            $(document).ready(function() {
                $('.tree').treegrid();
                $('.tree2').treegrid({
                    expanderExpandedClass: 'icon-minus-sign',
                    expanderCollapsedClass: 'icon-plus-sign'
                });
            });
        </script>
    <script>
      (function(i, s, o, g, r, a, m) {
        i['GoogleAnalyticsObject'] = r;
        i[r] = i[r] || function() {
          (i[r].q = i[r].q || []).push(arguments)
        }, i[r].l = 1 * new Date();
        a = s.createElement(o),
                m = s.getElementsByTagName(o)[0];
        a.async = 1;
        a.src = g;
        m.parentNode.insertBefore(a, m)
      })(window, document, 'script', '//www.google-analytics.com/analytics.js', 'ga');

      ga('create', 'UA-43342702-1', 'maxazan.github.io');
      ga('send', 'pageview');

    </script>
    </head>

    <body>
        <div class="container">

            <h1><a href="http://maxazan.github.io/jquery-treegrid/">TreeGrid</a> Bootstrap 2.3.2 examples</h1>

            <table class="table tree">
                <tr class="treegrid-1">
                    <td>Root node 1</td><td>Additional info</td>
                </tr>
                <tr class="treegrid-2 treegrid-parent-1">
                    <td>Node 1-1</td><td>Additional info</td>
                </tr>
                <tr class="treegrid-3 treegrid-parent-1">
                    <td>Node 1-2</td><td>Additional info</td>
                </tr>
                <tr class="treegrid-4 treegrid-parent-3">
                    <td>Node 1-2-1</td><td>Additional info</td>
                </tr>
                <tr class="treegrid-5">
                    <td>Root node 2</td><td>Additional info</td>
                </tr>
                <tr class="treegrid-6 treegrid-parent-5">
                    <td>Node 2-1</td><td>Additional info</td>
                </tr>
                <tr class="treegrid-7 treegrid-parent-5">
                    <td>Node 2-2</td><td>Additional info</td>
                </tr>
                <tr class="treegrid-8 treegrid-parent-7">
                    <td>Node 2-2-1</td><td>Additional info</td>
                </tr>        
            </table>
            <h3>Source</h3>

            <pre><code class="html">&lt;link href=&quot;bootstrap-2.3.2/css/bootstrap.css&quot; rel=&quot;stylesheet&quot;&gt;
&lt;link rel=&quot;stylesheet&quot; href=&quot;../css/jquery.treegrid.css&quot;&gt;

&lt;script type=&quot;text/javascript&quot; src=&quot;../js/jquery.treegrid.js&quot;&gt;&lt;/script&gt;
&lt;script type=&quot;text/javascript&quot; src=&quot;../js/jquery.treegrid.bootstrap2.js&quot;&gt;&lt;/script&gt;
&lt;script type=&quot;text/javascript&quot;&gt;
    $(document).ready(function() {
        $(&#39;.tree&#39;).treegrid();
    });
&lt;/script&gt;</code></pre>            
            <h2>Bootstrap grid example<br>
                table-bordered table-striped table-condensed<br>
                with custom expander</h2>
            <table class="table tree2 table-bordered table-striped table-condensed">
                <tr class="treegrid-1">
                    <td>Root node 1</td><td>Additional info</td>
                </tr>
                <tr class="treegrid-2 treegrid-parent-1">
                    <td>Node 1-1</td><td>Additional info</td>
                </tr>
                <tr class="treegrid-3 treegrid-parent-1">
                    <td>Node 1-2</td><td>Additional info</td>
                </tr>
                <tr class="treegrid-4 treegrid-parent-3">
                    <td>Node 1-2-1</td><td>Additional info</td>
                </tr>
                <tr class="treegrid-5">
                    <td>Root node 2</td><td>Additional info</td>
                </tr>
                <tr class="treegrid-6 treegrid-parent-5">
                    <td>Node 2-1</td><td>Additional info</td>
                </tr>
                <tr class="treegrid-7 treegrid-parent-5">
                    <td>Node 2-2</td><td>Additional info</td>
                </tr>
                <tr class="treegrid-8 treegrid-parent-7">
                    <td>Node 2-2-1</td><td>Additional info</td>
                </tr>        
            </table>	
            <h3>Source</h3>

            <pre><code class="html">&lt;link href=&quot;bootstrap-2.3.2/css/bootstrap.css&quot; rel=&quot;stylesheet&quot;&gt;
&lt;link rel=&quot;stylesheet&quot; href=&quot;../css/jquery.treegrid.css&quot;&gt;

&lt;script type=&quot;text/javascript&quot; src=&quot;../js/jquery.treegrid.js&quot;&gt;&lt;/script&gt;
&lt;script type=&quot;text/javascript&quot; src=&quot;../js/jquery.treegrid.bootstrap2.js&quot;&gt;&lt;/script&gt;
&lt;script type=&quot;text/javascript&quot;&gt;
    $(document).ready(function() {
                $('.tree').treegrid({
                    expanderExpandedClass: 'icon-minus-sign',
                    expanderCollapsedClass: 'icon-plus-sign'
                });
    });
&lt;/script&gt;</code></pre>             
        </div> <!-- /container -->


        <!-- Bootstrap core JavaScript
        ================================================== -->
        <!-- Placed at the end of the document so the pages load faster -->
    </body>
</html>
