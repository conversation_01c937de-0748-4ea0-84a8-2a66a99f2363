<!DOCTYPE html>
<html>
    <head>
        <meta charset="utf-8">
        <title>jQuery TreeGrid Unit tests</title>
        <link rel="stylesheet" href="qunit-1.12.0.css">
        <link rel="stylesheet" href="../css/jquery.treegrid.css">
    </head>
    <body>
        <div id="qunit"></div>
        <div id="qunit-fixture"></div>
        <table>
            <tr>
                <td>
                    <table id='tree-1' border="1" cellpadding="0" cellspacing="0">
                        <tr id="tree-head-1">
                            <th>1</th><th>2</th>
                        </tr>
                        <tr class="treegrid-1" id="node-1">
                            <td>1</td><td>Simple text of ...</td>
                        </tr>
                        <tr class="treegrid-2 treegrid-parent-1" id="node-1-1">
                            <td>1.1</td><td>Simple text of ...</td>
                        </tr>
                        <tr class='treegrid-3 treegrid-parent-2' id="node-1-1-1">
                            <td>1.1.1</td><td>Simple text of ...</td>
                        </tr>
                        <tr class='treegrid-9 treegrid-parent-2' id="node-1-1-2">
                            <td>1.1.2</td><td>Simple text of ...</td>
                        </tr>
                        <tr class='treegrid-10 treegrid-parent-9' id="node-1-1-2-1">
                            <td>1.1.2.1</td><td>Simple text of ...</td>
                        </tr>
                        <tr class='treegrid-4 treegrid-parent-1' id="node-1-2">
                            <td>1.2</td><td>Simple text of ...</td>
                        </tr>
                        <tr class='treegrid-5 treegrid-parent-1' id="node-1-3">
                            <td>1.3</td><td>Simple text of ...</td>
                        </tr>
                        <tr class='treegrid-6 treegrid-parent-5' id="node-1-3-1">
                            <td>1.3.1</td><td>Simple text of ...</td>
                        </tr>
                        <tr class='treegrid-7 treegrid-parent-1' id="node-1-4">
                            <td>1.4</td><td>Simple text of ...</td>
                        </tr>
                        <tr class='treegrid-8 treegrid-parent-7' id="node-1-4-1">
                            <td>1.4.1</td><td>Simple text of ...</td>
                        </tr>
                        <tr class='treegrid-12 treegrid-parent-1' id="node-1-5">
                            <td>1.5</td><td>Simple text of ...</td>
                        </tr>

                        <tr class="treegrid-11" id="node-2">
                            <td>2</td><td>Simple text of ...</td>
                        </tr>
                        <tr id="tree-summary-1">
                            <td>Total</td><td>343</td>
                        </tr>

                    </table>
                </td>
            </tr>
        </table>	  
        <hr>
        <table id='tree-2' border="1" cellpadding="0" cellspacing="0">
            <tr>
                <th>1</th><th>2</th>
            </tr>
            <tr class="treegrid-1" id="tnode-1">
                <td>1</td><td>Simple text of ...</td>
            </tr>
            <tr class="treegrid-2 treegrid-parent-1" id="tnode-1-1">
                <td>1.1</td><td>Simple text of ...</td>
            </tr>
            <tr class='treegrid-3 treegrid-parent-2' id="tnode-1-1-1">
                <td>1.1.1</td><td>Simple text of ...</td>
            </tr>
            <tr class='treegrid-9 treegrid-parent-2' id="tnode-1-1-2">
                <td>1.1.2</td><td>Simple text of ...</td>
            </tr>
            <tr class='treegrid-10 treegrid-parent-9' id="tnode-1-1-2-1">
                <td>1.1.2.1</td><td>Simple text of ...</td>
            </tr>
            <tr class='treegrid-4 treegrid-parent-1' id="tnode-1-2">
                <td>1.2</td><td>Simple text of ...</td>
            </tr>
            <tr class='treegrid-5 treegrid-parent-1' id="tnode-1-3">
                <td>1.3</td><td>Simple text of ...</td>
            </tr>
            <tr class='treegrid-6 treegrid-parent-5' id="tnode-1-3-1">
                <td>1.3.1</td><td>Simple text of ...</td>
            </tr>
            <tr class='treegrid-7 treegrid-parent-1' id="tnode-1-4">
                <td>1.4</td><td>Simple text of ...</td>
            </tr>
            <tr class='treegrid-8 treegrid-parent-7' id="tnode-1-4-1">
                <td>1.4.1</td><td>Simple text of ...</td>
            </tr>
            <tr class="treegrid-11" id="tnode-2">
                <td>2</td><td>Simple text of ...</td>
            </tr>
        </table>
        <hr>

        <table id='tree-3' border="1" cellpadding="0" cellspacing="0">
            <tr>
                <th>1</th><th>2</th>
            </tr>
            <tr class="treegrid-alfa1" id="anode-1">
                <td>1</td><td>Simple text of ...</td>
            </tr>
            <tr class="treegrid-alfa2 treegrid-parent-alfa1" id="anode-1-1">
                <td>1.1</td><td>Simple text of ...</td>
            </tr>
            <tr class='treegrid-alfa3 treegrid-parent-alfa2' id="anode-1-1-1">
                <td>1.1.1</td><td>Simple text of ...</td>
            </tr>
            <tr class='treegrid-alfa9 treegrid-parent-alfa2' id="anode-1-1-2">
                <td>1.1.2</td><td>Simple text of ...</td>
            </tr>
            <tr class='treegrid-alfa10 treegrid-parent-alfa9' id="anode-1-1-2-1">
                <td>1.1.2.1</td><td>Simple text of ...</td>
            </tr>
            <tr class='treegrid-alfa4 treegrid-parent-alfa1' id="anode-1-2">
                <td>1.2</td><td>Simple text of ...</td>
            </tr>
            <tr class='treegrid-alfa5 treegrid-parent-alfa1' id="anode-1-3">
                <td>1.3</td><td>Simple text of ...</td>
            </tr>
            <tr class='treegrid-alfa6 treegrid-parent-alfa5' id="anode-1-3-1">
                <td>1.3.1</td><td>Simple text of ...</td>
            </tr>
            <tr class='treegrid-alfa7 treegrid-parent-alfa1' id="anode-1-4">
                <td>1.4</td><td>Simple text of ...</td>
            </tr>
            <tr class='treegrid-alfa8 treegrid-parent-alfa7' id="anode-1-4-1">
                <td>1.4.1</td><td>Simple text of ...</td>
            </tr>
            <tr class="treegrid-alfa11" id="anode-2">
                <td>2</td><td>Simple text of ...</td>
            </tr>
        </table>

        <hr>
        Event tests
        <table id='tree-4' border="1" cellpadding="0" cellspacing="0">
            <tr>
                <th>1</th><th>2</th>
            </tr>
            <tr class="treegrid-alfa1" id="anode-1">
                <td>1</td><td>Simple text of ...</td>
            </tr>
            <tr class="treegrid-alfa2 treegrid-parent-alfa1" id="anode-1-1">
                <td>1.1</td><td>Simple text of ...</td>
            </tr>
            <tr class='treegrid-alfa3 treegrid-parent-alfa2' id="anode-1-1-1">
                <td>1.1.1</td><td>Simple text of ...</td>
            </tr>
            <tr class='treegrid-alfa9 treegrid-parent-alfa2' id="anode-1-1-2">
                <td>1.1.2</td><td>Simple text of ...</td>
            </tr>
            <tr class='treegrid-alfa10 treegrid-parent-alfa9' id="anode-1-1-2-1">
                <td>1.1.2.1</td><td>Simple text of ...</td>
            </tr>
            <tr class='treegrid-alfa4 treegrid-parent-alfa1' id="anode-1-2">
                <td>1.2</td><td>Simple text of ...</td>
            </tr>
            <tr class='treegrid-alfa5 treegrid-parent-alfa1' id="anode-1-3">
                <td>1.3</td><td>Simple text of ...</td>
            </tr>
            <tr class='treegrid-alfa6 treegrid-parent-alfa5' id="anode-1-3-1">
                <td>1.3.1</td><td>Simple text of ...</td>
            </tr>
            <tr class='treegrid-alfa7 treegrid-parent-alfa1' id="anode-1-4">
                <td>1.4</td><td>Simple text of ...</td>
            </tr>
            <tr class='treegrid-alfa8 treegrid-parent-alfa7' id="anode-1-4-1">
                <td>1.4.1</td><td>Simple text of ...</td>
            </tr>
            <tr class="treegrid-alfa11" id="anode-2">
                <td>2</td><td>Simple text of ...</td>
            </tr>
        </table>

        <hr>
        <script src="../docs/js/jquery.min.js"></script>
        <script src="../js/jquery.treegrid.min.js"></script>
        <script src="../js/jquery.cookie.js"></script>
        <script>
            var globalCounter = 0;
            var saveStateName = 'state-save-4';
            $("#tree-1").treegrid({initialState: 'collapsed'});
            $("#tree-2").treegrid({treeColumn: 1, initialState: 'expanded', 'saveState': true, 'saveStateName': saveStateName});
            $("#tree-3").treegrid({initialState: 'collapsed'});
            $("#tree-4").treegrid({onChange: function() {
                    globalCounter++;
                }, onCollapse: function() {
                    globalCounter++;
                }, onExpand: function() {
                    globalCounter++;
                }});
        </script>
        <script src="qunit-1.12.0.js"></script>
        <script src="tests.js"></script>

    </body>
</html>