<?php

// Override the installer route to use our custom controller
Route::group(['prefix' => 'install', 'as' => 'LaravelInstaller::', 'middleware' => ['web', 'install']], function () {
    // Override the saveWizard route to use our custom controller
    Route::post('environment/saveWizard', [
        'as' => 'environmentSaveWizard',
        'uses' => 'App\Http\Controllers\CustomEnvironmentController@saveWizard',
    ]);
});
