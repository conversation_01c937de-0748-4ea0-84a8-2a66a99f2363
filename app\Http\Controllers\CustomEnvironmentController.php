<?php

namespace App\Http\Controllers;

use <PERSON>chid<PERSON><PERSON><PERSON><PERSON>\LaravelInstaller\Controllers\EnvironmentController;
use Illuminate\Http\Request;
use Illuminate\Routing\Redirector;
use Illuminate\Support\Facades\Validator;
use <PERSON>chid<PERSON><PERSON><PERSON><PERSON>\LaravelInstaller\Events\EnvironmentSaved;

class CustomEnvironmentController extends EnvironmentController
{
    /**
     * Processes the newly saved environment configuration (Form Wizard).
     * Override to bypass purchase code validation.
     *
     * @param  Request  $request
     * @param  Redirector  $redirect
     * @return \Illuminate\Http\RedirectResponse
     */
    public function saveWizard(Request $request, Redirector $redirect)
    {
        $rules = config('installer.environment.form.rules');
        $messages = [
            'environment_custom.required_if' => trans('installer_messages.environment.wizard.form.name_required'),
        ];

        $validator = Validator::make($request->all(), $rules, $messages);

        if ($validator->fails()) {
            return $redirect->route('LaravelInstaller::environmentWizard')->withInput()->withErrors($validator->errors());
        }

        if (! $this->checkDatabaseConnection($request)) {
            return $redirect->route('LaravelInstaller::environmentWizard')->withInput()->withErrors([
                'database_connection' => trans('installer_messages.environment.wizard.form.db_connection_failed'),
            ]);
        }

        // BYPASS PURCHASE CODE VALIDATION
        // Skip the Envato purchase code validation entirely
        // Set dummy success values to proceed
        $request->request->add([
            'status' => 'success', 
            'unique_code' => 'bypassed-' . time()
        ]);

        $results = $this->EnvironmentManager->saveFileWizard($request);

        event(new EnvironmentSaved($request));

        return $redirect->route('LaravelInstaller::database')
                    ->with(['results' => $results]);
    }
}
