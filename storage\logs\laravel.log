[2025-07-31 19:10:47] production.ERROR: Uncaught ReflectionException: Class "App\Providers\ServicePolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\S...')
#1 [internal function]: {closure}('App\\Providers\\S...', 'App\\Providers\\S...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\ServicePolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\S...')
#1 [internal function]: {closure}('App\\\\Providers\\\\S...', 'App\\\\Providers\\\\S...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-07-31 19:11:34] production.ERROR: Uncaught ReflectionException: Class "App\Providers\ServicePolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\S...')
#1 [internal function]: {closure}('App\\Providers\\S...', 'App\\Providers\\S...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\ServicePolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\S...')
#1 [internal function]: {closure}('App\\\\Providers\\\\S...', 'App\\\\Providers\\\\S...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-07-31 19:12:34] production.ERROR: Uncaught ReflectionException: Class "App\Providers\ServicePolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\S...')
#1 [internal function]: {closure}('App\\Providers\\S...', 'App\\Providers\\S...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\ServicePolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\S...')
#1 [internal function]: {closure}('App\\\\Providers\\\\S...', 'App\\\\Providers\\\\S...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-07-31 19:13:34] production.ERROR: Uncaught ReflectionException: Class "App\Providers\ServicePolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\S...')
#1 [internal function]: {closure}('App\\Providers\\S...', 'App\\Providers\\S...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\ServicePolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\S...')
#1 [internal function]: {closure}('App\\\\Providers\\\\S...', 'App\\\\Providers\\\\S...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-07-31 19:14:34] production.ERROR: Uncaught ReflectionException: Class "App\Providers\ServicePolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\S...')
#1 [internal function]: {closure}('App\\Providers\\S...', 'App\\Providers\\S...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\ServicePolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\S...')
#1 [internal function]: {closure}('App\\\\Providers\\\\S...', 'App\\\\Providers\\\\S...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-07-31 19:15:34] production.ERROR: Uncaught ReflectionException: Class "App\Providers\ServicePolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\S...')
#1 [internal function]: {closure}('App\\Providers\\S...', 'App\\Providers\\S...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\ServicePolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\S...')
#1 [internal function]: {closure}('App\\\\Providers\\\\S...', 'App\\\\Providers\\\\S...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-07-31 19:16:34] production.ERROR: Uncaught ReflectionException: Class "App\Providers\ServicePolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\S...')
#1 [internal function]: {closure}('App\\Providers\\S...', 'App\\Providers\\S...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\ServicePolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\S...')
#1 [internal function]: {closure}('App\\\\Providers\\\\S...', 'App\\\\Providers\\\\S...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-07-31 19:17:34] production.ERROR: Uncaught ReflectionException: Class "App\Providers\ServicePolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\S...')
#1 [internal function]: {closure}('App\\Providers\\S...', 'App\\Providers\\S...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\ServicePolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\S...')
#1 [internal function]: {closure}('App\\\\Providers\\\\S...', 'App\\\\Providers\\\\S...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-07-31 19:18:34] production.ERROR: Uncaught ReflectionException: Class "App\Providers\ServicePolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\S...')
#1 [internal function]: {closure}('App\\Providers\\S...', 'App\\Providers\\S...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\ServicePolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\S...')
#1 [internal function]: {closure}('App\\\\Providers\\\\S...', 'App\\\\Providers\\\\S...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
[2025-07-31 19:19:34] production.ERROR: Uncaught ReflectionException: Class "App\Providers\ServicePolicy" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\Providers\\S...')
#1 [internal function]: {closure}('App\\Providers\\S...', 'App\\Providers\\S...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown {"exception":"[object] (Symfony\\Component\\ErrorHandler\\Error\\FatalError(code: 0): Uncaught ReflectionException: Class \"App\\Providers\\ServicePolicy\" does not exist in Command line code:1
Stack trace:
#0 Command line code(1): ReflectionClass->__construct('App\\\\Providers\\\\S...')
#1 [internal function]: {closure}('App\\\\Providers\\\\S...', 'App\\\\Providers\\\\S...')
#2 Command line code(1): array_map(Object(Closure), Array, Array)
#3 {main}
  thrown at Command line code:1)
[stacktrace]
#0 {main}
"} 
