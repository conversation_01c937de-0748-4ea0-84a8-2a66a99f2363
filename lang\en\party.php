<?php

return [

    'info'                      => 'Party Info',
    'party'                     => 'Party',
    'parties'                   => 'Parties',
    'list'                      => 'Party List',
    'create_party'              => 'Create Party',
    'add_party'                 => 'Add Party',
    'details'                   => 'Party Details',
    'update_party'              => 'Update Party',
    'notify_party'              => 'Notify Party',
    'empty_email'               => "The party does not have an email address",

    'name'                      => "Party Name",
    'total'                     => "Total partys",
    'contacts'                  => "Contacts",
    'import_contacts'           => "Import Contacts",
    'credit_and_balance'        => "Credit & Balance",
    'billing_address'           => "Billing Address",
    'shipping_address'          => "Shipping Address",
    'to_pay'                    => "To Pay",
    'to_receive'                => "To Receive",
    'credit_limit'                => "Credit Limit",
    'failed_to_record_party_transactions'   => "Failed to record party transaction!",
    'party_credit_limit_is_exceeding'       => 'The party\'s credit limit of :credit_limit is exceeding the total due of :total_due.',

    'gstin'                     => "GSTIN/UIN",
    'party_name'                => "Party Name",
    'state_of_supply'           => "State of Supply",
    'transaction'               => "Party Transaction",
    'transactions'              => "Party Transactions",
    'payment'                   => "Party Payment",
    'wholesaler'                => "Wholesaler",
    'retailer'                  => "Retailer",
    'wholesalers'               => "Wholesalers",
    'retailers'                 => "Retailers",
    //1.4.1
    'due_balance'               => "Party Due Balance",
    'customer_items'            => "Customer Items",
];
