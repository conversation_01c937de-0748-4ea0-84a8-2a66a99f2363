<?php

return [
    'app_token'    => 'xbm69iyogfd8trja7e42l1wpzsv3kn',
    /*
    |--------------------------------------------------------------------------
    | Server Requirements
    |--------------------------------------------------------------------------
    |
    | This is the default Laravel server requirements, you can add as many
    | as your application require, we check if the extension is enabled
    | by looping through the array and run "extension_loaded" on it.
    |
    */
    'core' => [
        'minPhpVersion' => '8.2',
    ],
    'final' => [
        'key' => true,
        'publish' => false,
    ],
    'requirements' => [
        'php' => [
            'mysqlnd',


            'openssl',
            'mbstring',
            'tokenizer',
            'JSON',
            'cURL',
            'intl',
            'zip',
            'gd',
            'pdo',
            //'nd_pdo_mysql',//Hostinger.com only has this one,
            //'pdo_mysql', //hostinger doesn't have pdo_mysql extension


        ],
        'apache' => [
            'mod_rewrite',
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Folders Permissions
    |--------------------------------------------------------------------------
    |
    | This is the default Laravel folders permissions, if your application
    | requires more permissions just add them to the array list bellow.
    |
    */
    'permissions' => [
        'storage/framework/'     => '775',
        'storage/logs/'          => '775',
        'bootstrap/cache/'       => '775',
    ],

    /*
    |--------------------------------------------------------------------------
    | Environment Form Wizard Validation Rules & Messages
    |--------------------------------------------------------------------------
    |
    | This are the default form field validation rules. Available Rules:
    | https://laravel.com/docs/5.4/validation#available-validation-rules
    |
    */
    'environment' => [
        'form' => [
            'rules' => [
                'app_name'              => 'nullable|string|max:50',
                'environment'           => 'nullable|string|max:50',
                'environment_custom'    => 'nullable|max:50',
                'app_debug'             => 'nullable|string',
                'app_log_level'         => 'nullable|string|max:50',
                'app_url'               => 'nullable|url',
                'database_connection'   => 'nullable|string|max:50',
                'database_hostname'     => 'nullable|string|max:50',
                'database_port'         => 'nullable|numeric',
                'database_name'         => 'nullable|string|max:50',
                'database_username'     => 'nullable|string|max:50',
                'database_password'     => 'nullable|string|max:50',
                'broadcast_driver'      => 'nullable|string|max:50',
                'cache_driver'          => 'nullable|string|max:50',
                'session_driver'        => 'nullable|string|max:50',
                'queue_driver'          => 'nullable|string|max:50',
                'redis_hostname'        => 'nullable|string|max:50',
                'redis_password'        => 'nullable|string|max:50',
                'redis_port'            => 'nullable|numeric',
                'mail_driver'           => 'nullable|string|max:50',
                'mail_host'             => 'nullable|string|max:50',
                'mail_port'             => 'nullable|string|max:50',
                'mail_username'         => 'nullable|string|max:50',
                'mail_password'         => 'nullable|string|max:50',
                'mail_encryption'       => 'nullable|string|max:50',
                'pusher_app_id'         => 'max:50',
                'pusher_app_key'        => 'max:50',
                'pusher_app_secret'     => 'max:50',
                'envato_username'       => 'nullable|string|max:50',
                'user_email'            => 'nullable|email|max:50',
                'envato_purchase_code'  => 'nullable|string|max:50',
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Installed Middleware Options
    |--------------------------------------------------------------------------
    | Different available status switch configuration for the
    | canInstall middleware located in `canInstall.php`.
    |
    */
    'installed' => [
        'redirectOptions' => [
            'route' => [
                'name' => 'welcome',
                'data' => [],
            ],
            'abort' => [
                'type' => '404',
            ],
            'dump' => [
                'data' => 'Dumping a not found message.',
            ],
        ],
    ],

    /*
    |--------------------------------------------------------------------------
    | Selected Installed Middleware Option
    |--------------------------------------------------------------------------
    | The selected option fo what happens when an installer instance has been
    | Default output is to `/resources/views/error/404.blade.php` if none.
    | The available middleware options include:
    | route, abort, dump, 404, default, ''
    |
    */
    'installedAlreadyAction' => '',

    /*
    |--------------------------------------------------------------------------
    | Updater Enabled
    |--------------------------------------------------------------------------
    | Can the application run the '/update' route with the migrations.
    | The default option is set to False if none is present.
    | Boolean value
    |
    */
    'updaterEnabled' => 'true',

];
