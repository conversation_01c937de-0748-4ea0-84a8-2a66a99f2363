<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('products', function (Blueprint $table) {
            $table->id();
            
            $table->string('product_code')->nullable();
            
            $table->decimal('purchase_price', 8, 2)->default(0);
            $table->decimal('price', 8, 2)->default(0);
            $table->decimal('sales_price', 8, 2)->default(0);

            $table->unsignedBigInteger('tax_id')->nullable();
            $table->enum('tax_type', ['inclusive', 'exclusive'])->default('inclusive');

            $table->string('image_path')->nullable();
            $table->string('name');
            $table->text('description')->nullable();

            $table->enum('status', ['active','inactive'])->default('active');
            
            $table->timestamps();

            $table->foreign('tax_id')->references('id')->on('taxes');
          });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('products');
    }
};
