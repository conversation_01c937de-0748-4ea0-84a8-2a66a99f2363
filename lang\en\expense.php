<?php

return [
    'expense'                       => 'Expense',
    'details'                       => 'Expense Details',
    'expense_category'              => 'Expense Category',
    'list'                          => 'Expense List',
    'create'                        => 'Create Expense',
    'expense_type'                  => 'Expense Type',//Actually its acount
    'code'                          => 'Expense Code',
    'number'                        => 'Expense Number',
    'items'                         => 'Expense Items',
    'update'                        => 'Update Expense',
    'report'                        => 'Expense Report',
    'item'                          => 'Expense Item',
    'item_report'                   => 'Expense Item Report',
    'payment_report'                => 'Expense Payment Report',
    'total_expenses'                => 'Total Expense',

    'category' => [
                    'category'      => 'Category',
                    'type'          => 'Category Type',
                    'details'       => 'Expense Category Details',
                    'create'        => 'Create Category',
                    'list'          => 'Category List',
                    'update'        => 'Update Category',
                ],
    //1.4.8
    'subcategory' => [
                    'subcategory'      => 'Subcategory',
                    'type'          => 'Subcategory Type',
                    'details'       => 'Expense Subcategory Details',
                    'create'        => 'Create Subcategory',
                    'list'          => 'Subcategory List',
                    'update'        => 'Update Subcategory',
                ],
    //1.5.1
    'add'                           => 'Add Expense',



];
