<?php

return [

    'info'                      => 'Payment Info',
    'type'                      => 'Payment Type',
    'types'                     => 'Payment Types',
    'payment'                   => 'Payment',
    'payments'                  => 'Payments',
    'create'                    => 'Create Payment',
    'add'                       => 'Add Payment',
    'details'                   => 'Payment Details',
    'update'                    => 'Update Payment',
    'amount'                    => 'Amount',
    'note'                      => 'Payment Note',
    'history'                   => 'Payment History',
    'status'                    => 'Payment Status',
    'paid'                      => 'Paid',
    'paid_amount'               => 'Paid Amount',
    'balance'                   => 'Balance',
    'date'                      => 'Payment Date',
    'received'                  => 'Received Payment',
    'payment_type'              => 'Payment Type',

    'add_payment_type'          => 'Add Payment Type',

    'make_payment'              => 'Make Payment',

    'transaction_date'          => 'Transaction Date',

    'receipt_no'                => 'Receipt No.',



    'summary'                   => 'Payment Summry',

    'paid_to'                   => 'Paid To',

    'payment_out'               => 'Payment Out',

    'payment_in'                => 'Payment In',

    'payment_receipt'           => 'Payment Receipt',

    'previously_paid'           => 'Previously Paid',

    'received_from'             => 'Received From',

    'receive_payment'           => 'Receive Payment',

    'cash'                   => 'Cash',

    'cheque'                    => 'Cheque',


    'payment_should_not_be_greater_than_grand_total'    => 'Payment should not be greater than Grand Total.',

    'please_select_payment_type'                        => 'Please Select Payment Type',

    'missed_to_select_payment_type'                     => 'Missed to Select Payment Type',

    'paid_payment_not_equal_to_grand_total'             => 'Paid Amount Should be equal to Grand Total!',

    'failed_to_update_paid_amount'                      => 'Failed to update Paid Amount!',

    'failed_to_update_account'                          => 'Failed to update Account!',

    'failed_to_record_payment_transactions'             => 'Failed to Record Payment Transaction!',

    'failed_to_delete_payment_transactions'             => 'Failed to Delete Payment Transaction!',

    'no_payment_history_found'                          => 'No payment history found.',

    'failed_to_record_cheque_payment_transactions'      => 'Failed to Record Cheque Payment Transaction!',



    'cash_in_hand'                          => 'Cash In Hand',

    'cash_and_bank'                          => 'Cash & Bank',

    'cash_adjust'                          => 'Cash Adjust',

    'adjustment_type'                      => 'Adjustment Type',

    'cheques'                               => 'Cheques',

    'cheque_deposit'                        => 'Cheque Deposit',

    'deposit_date'                          => 'Deposit Date',

    'deposit_to'                            => 'Deposit To',

    'cheque_reopened_successfully'          => 'Cheque Re-opened successfully',

    'transfer_cheque'                       => 'Transfer Cheque',

    'transfer_date'                         => 'Transfer Date',

    'bank_accounts'                         => 'Bank Accounts',

    'add_bank'                              => 'Add Bank',

    'create_account'                        => 'Create Account',

    'bank_details'                          => 'Bank Details',

    'bank_name'                             => 'Bank Name',

    'account_number'                        => 'Account Number',

    'bank_code'                             => 'Bank Code/IFSC',

    'update_bank_account'                   => 'Update Bank Account',

    'bank'                                  => 'Bank',

    'bank_transactions'                     => 'Bank Transactions',

    'print_bank_details_on_invoice'         => 'Print Bank Details on Invoice',

    'cash_flow'                             => 'Cash flow',

    'bank_statement'                        => 'Bank Statement',

    'cash_in'                               => 'Cash In',

    'cash_out'                              => 'Cash Out',

    'running_cash'                          => 'Running Cash',

    'withdrawal_amount'                     => 'Withdrawal Amount',

    'deposit_amount'                        => 'Deposit Amount',

    'you_pay'                               => 'You Pay',

    'you_collect'                           => 'You Collect',

    'adjust_invoices'                       => 'Adjust Invoices',

    'adjust_bills'                          => 'Adjust Bill',

    'adjust'                                => 'Adjust',

    'adjust_amount'                         => 'Adjust Amount',

    'due_payments'                          => 'Due Payments',



    'no_balance'                            => 'No Balance',

    'payment_direction'                     => 'Payment Direction',

    'payment_receivables'                   => 'Payment Receivables',

    'payment_paybles'                       => 'Payment Paybles',

    /*1.1.2*/
    'change_return'                         => 'Change Return',
    'due_payment'                           => 'Due Payment',
    'due_payments'                          => 'Due Payments',
    'due_payment_report'                    => 'Due Payment Report',

    'receipt'                               => 'Receipt',

    //1.5.1
    'paid_amount_should_not_be_less_than_zero'                               => 'Paid amount should not be less than zero',

];
