<!doctype html>
<html>
  <head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="chrome=1">
    <title>Jquery-treegrid basic example</title>

    <link rel="stylesheet" href="../css/styles.css">
    <link rel="stylesheet" href="../css/default.min.css">
    <link rel="stylesheet" href="../../css/jquery.treegrid.css">

    <script type="text/javascript" src="../js/jquery.min.js"></script>
    <script type="text/javascript" src="../../js/jquery.treegrid.js"></script>

    <script type="text/javascript" src="../js/highlight.min.js"></script>
    <script>hljs.initHighlightingOnLoad();</script>
    
    <script type="text/javascript">
      $(document).ready(function() {
      	//create huge treetable
      	var count_root_elements=100;
      	var count_deep=10;
      	console.log('init');
      	for(var i=0; i<count_root_elements; i++){
      		console.log('add');
      		var tr=$("<tr></tr>").addClass("treegrid-"+i+"-0").appendTo($('.tree')).html("<td>Root node "+i+"</td><td>Additional info</td>");

	      	for(var j=1; j<count_deep; j++){
				$("<tr></tr>").addClass("treegrid-"+i+"-"+j).addClass("treegrid-parent-"+i+"-"+(j-1)).appendTo($('.tree')).html("<td>Child node "+i+"-"+j+"</td><td>Additional info</td>");
	      	}
      	}

        $('.tree').treegrid();
      });
    </script>


    
    <meta name="viewport" content="width=device-width, initial-scale=1, user-scalable=no">
    <!--[if lt IE 9]>
    <script src="//html5shiv.googlecode.com/svn/trunk/html5.js"></script>
    <![endif]-->
  </head>
  <body>
    <div class="wrapper">
	  

      <h1><a href="http://maxazan.github.io/jquery-treegrid/">TreeGrid</a> Big data example</h1>

      <table class="tree"></table>	  

      <h2>Code</h2>
        
      <pre>
<code class='html'>
&lt;!doctype html&gt;
&lt;html&gt;
  &lt;head&gt;
    &lt;meta charset=&quot;utf-8&quot;&gt;
    &lt;meta http-equiv=&quot;X-UA-Compatible&quot; content=&quot;chrome=1&quot;&gt;
    &lt;title&gt;Jquery-treegrid basic example&lt;/title&gt;

    &lt;link rel=&quot;stylesheet&quot; href=&quot;../css/jquery.treegrid.css&quot;&gt;

    &lt;script src=&quot;http://ajax.googleapis.com/ajax/libs/jquery/1.10.2/jquery.min.js&quot;&gt;&lt;/script&gt;
    &lt;script type=&quot;text/javascript&quot; src=&quot;../js/jquery.treegrid.js&quot;&gt;&lt;/script&gt;
    &lt;script type=&quot;text/javascript&quot;&gt;
      $(document).ready(function() {
        $('.tree').treegrid();
      });
    &lt;/script&gt;

  &lt;/head&gt;
  &lt;body&gt;

      &lt;table class=&quot;tree&quot;&gt;
        &lt;tr class=&quot;treegrid-1&quot;&gt;
          &lt;td&gt;Root node&lt;/td&gt;&lt;td&gt;Additional info&lt;/td&gt;
        &lt;/tr&gt;
        &lt;tr class=&quot;treegrid-2 treegrid-parent-1&quot;&gt;
          &lt;td&gt;Node 1-1&lt;/td&gt;&lt;td&gt;Additional info&lt;/td&gt;
        &lt;/tr&gt;
        &lt;tr class=&quot;treegrid-3 treegrid-parent-1&quot;&gt;
          &lt;td&gt;Node 1-2&lt;/td&gt;&lt;td&gt;Additional info&lt;/td&gt;
        &lt;/tr&gt;
        &lt;tr class=&quot;treegrid-4 treegrid-parent-3&quot;&gt;
          &lt;td&gt;Node 1-2-1&lt;/td&gt;&lt;td&gt;Additional info&lt;/td&gt;
        &lt;/tr&gt;
      &lt;/table&gt;	  

  &lt;/body&gt;
&lt;/html&gt;
</code>
      </pre>
    </div>

    <script type="text/javascript">
      var metas = document.getElementsByTagName('meta');
      var i;
      if (navigator.userAgent.match(/iPhone/i)) {
        for (i = 0; i < metas.length; i++) {
          if (metas[i].name == "viewport") {
            metas[i].content = "width=device-width, minimum-scale=1.0, maximum-scale=1.0";
          }
        }
        document.addEventListener("gesturestart", gestureStart, false);
      }
      function gestureStart() {
        for (i = 0; i < metas.length; i++) {
          if (metas[i].name == "viewport") {
            metas[i].content = "width=device-width, minimum-scale=0.25, maximum-scale=1.6";
          }
        }
      }
      </script>

  </body>
</html>